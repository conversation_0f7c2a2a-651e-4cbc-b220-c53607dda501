import 'package:cal/common/widgets/metric_card.dart';
import 'package:cal/features/home/<USER>/bloc/nutrition_bloc/bloc/nutrition_bloc.dart';
import 'package:cal/generated/assets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:cal/common/extentions/colors_extension.dart';

import '../../../../generated/locale_keys.g.dart';

List<String> descriptions = [
  LocaleKeys.home_remaining_fats,
  LocaleKeys.home_remaining_carbs,
  LocaleKeys.home_remaining_proteins,
];

class NutritionSummary extends StatelessWidget {
  const NutritionSummary({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<NutritionBloc, NutritionState>(
      builder: (context, state) {
        final remainingCalories = (state.targetCalories - state.consumedCalories).toStringAsFixed(0);
        final caloriesProgress = calculateSafeProgress(state.targetCalories, state.consumedCalories);

        final consumedValues = [
          state.consumedFat,
          state.consumedCarbs,
          state.consumedProtein,
        ];
        final totalValues = [
          state.targetFat,
          state.targetCarbs,
          state.targetProtein,
        ];
        List<String> descriptions = [
          LocaleKeys.home_remaining_fats,
          LocaleKeys.home_remaining_carbs,
          LocaleKeys.home_remaining_proteins,
        ];

        List<String> images = [
          Assets.imagesFats,
          Assets.imagesCarbs,
          Assets.imagesProtien,
        ];

        List<Color> colors = [
          const Color(0xff4277FF), // Fat color (blue)
          const Color(0xffFFA76E), // Carbs color (orange)
          const Color(0xffE55B35), // Protein color (red)
        ];

        return Column(
          children: [
            MetricCard(
              title: remainingCalories,
              description: LocaleKeys.home_remaining_cals.tr(),
              color: context.primaryColor,
              progress: caloriesProgress,
              progressHeight: 84,
              progressWidth: 84,
              icon: Assets.imagesCals,
            ),
            const SizedBox(height: 12),
            Row(
              children: List.generate(3, (i) {
                final consumed = consumedValues[i];
                final total = totalValues[i];
                final double remaining = total - consumed;
                final String displayValue = remaining <= 0 ? '0' : remaining.toStringAsFixed(0);
                final bool isOverConsumed = consumed > total && total > 0;
                final double progress = total > 0 ? (consumed / total).clamp(0.0, 1.0) : 0.0;

                return Expanded(
                  child: Padding(
                    padding: EdgeInsets.only(
                      right: i < 2 ? 6.0 : 0.0,
                      left: i > 0 ? 6.0 : 0.0,
                    ),
                    child: MetricCard(
                      title: displayValue,
                      horizontal: false,
                      strokedWidth: 5,
                      description: descriptions[i].tr(),
                      color: isOverConsumed ? Colors.red : colors[i],
                      progress: progress,
                      progressHeight: 65,
                      progressWidth: 65,
                      icon: images[i],
                    ),
                  ),
                );
              }),
            ),
          ],
        );
      },
    );
  }
}
