import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/extentions/navigation_extensions.dart';
import 'package:cal/common/extentions/size_extension.dart';
import 'package:cal/common/widgets/app_text.dart';
import 'package:cal/core/di/injection.dart';
import 'package:cal/features/quick_actions/exercise/data/models/exercise_save_ai_model.dart';
import 'package:cal/features/quick_actions/exercise/presentation/bloc/exercise_bloc.dart';
import 'package:cal/features/settings/presentation/widgets/edit_ingredient_card.dart';
import 'package:cal/generated/assets.dart';
import 'package:cal/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ManualExerciseScreen extends StatefulWidget {
  const ManualExerciseScreen({super.key});

  @override
  State<ManualExerciseScreen> createState() => _ManualExerciseScreenState();
}

class _ManualExerciseScreenState extends State<ManualExerciseScreen> {
  final TextEditingController _caloriesController = TextEditingController();

  final FocusNode _caloriesNode = FocusNode();

  @override
  void dispose() {
    _caloriesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider<ExerciseBloc>(
      create: (context) => getIt<ExerciseBloc>(),
      child: BlocListener<ExerciseBloc, ExerciseState>(
        listener: (context, state) {},
        child: Scaffold(
          appBar: AppBar(
            toolbarHeight: context.screenHeight * 0.1,
            title: AppText.titleLarge(
              LocaleKeys.exercise_manual_entry_title.tr(),
              color: context.onSecondary,
            ),
            centerTitle: true,
            leading: IconButton(
              icon: const Icon(Icons.arrow_back),
              onPressed: () {
                context.pop();
              },
            ),
          ),
          body: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                EditIngredientCard(
                  color: context.primaryColor,
                  image: Assets.imagesCals,
                  title: "السعرات المستهدفة",
                  controller: _caloriesController,
                  focusNode: _caloriesNode,
                  onChanged: (val) {},
                  value: 10.toString(),
                ),
                const Spacer(),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () {
                      final exercise = ExerciseSaveAiModel(
                        description: _caloriesController.text,
                      );
                      context.read<ExerciseBloc>().add(SaveExerciseAi(exercise: exercise));
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: context.primaryColor,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: AppText.titleLarge(
                      'اضافة التمرين',
                      color: context.onPrimaryColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
