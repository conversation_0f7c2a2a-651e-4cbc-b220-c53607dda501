import 'dart:developer';

import 'package:cal/core/local_models/daily_data_model/daily_data_model.dart';
import 'package:injectable/injectable.dart';

import 'package:isar/isar.dart';

abstract class HomeLocalDataSource {
  Future<DailyUserDataModel> getDailyUserData(DateTime date);

  Future<void> updateDailyUserData(DailyUserDataModel dailyUserData);
}

@LazySingleton(as: HomeLocalDataSource)
class HomeLocalDataSourceImpl implements HomeLocalDataSource {
  final Isar isar;

  HomeLocalDataSourceImpl({required this.isar});

  @override
  Future<DailyUserDataModel> getDailyUserData(DateTime date) async {
    final normalizedDate = DateTime(date.year, date.month, date.day);
    final existing = await isar.dailyUserDataModels.filter().dateEqualTo(normalizedDate).findFirst();

    if (existing != null) return existing;

    return await _createFromPreviousDay(normalizedDate);
  }

  Future<DailyUserDataModel> _createFromPreviousDay(DateTime date) async {
    log("_createFromPreviousDay");

    DateTime searchDate = date.subtract(const Duration(days: 1));
    DailyUserDataModel? previous;

    while (true) {
      final result = await isar.dailyUserDataModels.filter().dateEqualTo(DateTime(searchDate.year, searchDate.month, searchDate.day)).findFirst();

      if (result != null && (result.targetCalories != 0 || result.targetCarbs != 0 || result.targetFat != 0 || result.targetProtein != 0)) {
        previous = result;
        break;
      }

      searchDate = searchDate.subtract(const Duration(days: 1));

      if (searchDate.isBefore(DateTime(2000))) break;
    }

    final newModel = DailyUserDataModel(
      date: date,
      targetCalories: previous?.targetCalories ?? 0,
      targetCarbs: previous?.targetCarbs ?? 0,
      targetFat: previous?.targetFat ?? 0,
      targetProtein: previous?.targetProtein ?? 0,
      consumedCalories: 0,
      consumedCarbs: 0,
      consumedFat: 0,
      consumedProtein: 0,
      burnedCalories: 0,
      weight: previous?.weight ?? 0,
      height: previous?.height ?? 0,
      bmi: previous?.bmi ?? 0,
    );

    await isar.writeTxn(() async {
      await isar.dailyUserDataModels.put(newModel);
    });

    return newModel;
  }

  @override
  Future<void> updateDailyUserData(DailyUserDataModel dailyUserData) async {
    await isar.writeTxn(() async {
      final existing = await isar.dailyUserDataModels.filter().dateEqualTo(dailyUserData.date).findFirst();

      if (existing != null) {
        // Use Isar's cascade notation to update the existing model
        await isar.dailyUserDataModels.put(dailyUserData..id = existing.id);
      } else {
        await isar.dailyUserDataModels.put(dailyUserData);
      }
    });
  }
}
